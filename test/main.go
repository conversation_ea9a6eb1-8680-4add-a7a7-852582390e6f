package main

import (
	"fmt"
	"net/url"
)

func main() {
	// 原始 URL
	rawURL := "https://rr3---sn-npoe7nlz.googlevideo.com/videoplayback?expire=1753977040&ei=cDyLaJyfA7Gwlu8PlN_uwAo&ip=***************&id=o-AJq3uKaXbFNTfiPpcyPVUr-BLWYjeYqUlDoZJguPOqaw&itag=140&source=youtube&requiressl=yes&xpc=EgVo2aDSNQ%3D%3D&bui=AY1jyLPH06SRGJfo0AVUSuAlK0AWvLHjHKGmGoZZ4N6jgXt_zSd2nLIRUCcq7P04X-7JR-e8bZKiWL89&spc=l3OVKVokpgkK2_wJwpX9wtn9VLvFpUgQ-R4c26tIn74xOIjB9IvlRdjVC1vCZoHctEA&vprv=1&svpuc=1&xtags=acont%3Ddubbed-auto%3Alang%3Dde-DE&mime=audio%2Fmp4&ns=LhNUneKzf-bNTR7BhAF1P9cQ&rqh=1&gir=yes&clen=23957567&dur=1480.272&lmt=1734320354539955&keepalive=yes&fexp=24350590,24350737,24350827,24351316,24351318,24351528,24352220,24352460,24352517,24352519,24352559,24352568,24352573,24352597,24352599,51355912&c=WEB&sefc=1&txp=3318224&n=4HN0HB5GRb_t1Q&sparams=expire%2Cei%2Cip%2Cid%2Citag%2Csource%2Crequiressl%2Cxpc%2Cbui%2Cspc%2Cvprv%2Csvpuc%2Cxtags%2Cmime%2Cns%2Crqh%2Cgir%2Cclen%2Cdur%2Clmt&sig=AJfQdSswRQIgH3ZHl8FMueU91-IHcTnxku2xTbX4TdoyDmpYFwnQh3sCIQCdaYl4m-hD7G-sSZjLaOjDZYFUvwNZgnDAmyprHVcouA%3D%3D&pot=MnT-_Uzcm7ZX0P7FA_9lgxO-hAZ3CJ_28dy9ZQ-_7-r9S84LIEquOMMBzHxlQGhWzpRYeLBVzKsBEu89STe-12RDxBRiU3-zXMMnnoS0AE1tJUcYGXuuFsDPhD8aB9b1x9devE89NHz44HCy6LIBsaRShBGIYQ%3D%3D&range=0-&redirect_counter=1&cm2rm=sn-vgqezl76&rrc=80&req_id=275f27cb3028a6e9&cms_redirect=yes&cmsv=e&met=1753955471,&mh=NC&mip=**************&mm=34&mn=sn-npoe7nlz&ms=ltu&mt=1753955081&mv=m&mvi=3&pl=24&rms=ltu,au&lsparams=met,mh,mip,mm,mn,ms,mv,mvi,pl,rms&lsig=APaTxxMwRgIhAKoq8BXz8tdfT9TJ_YYKtVcFoWBXL88RVv8fuP13QjksAiEAhFtHP-zJMy2W3GcS7XEAeU-JG5QxyW9efH8fj_i13Nw%3D"

	// 解析 URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		fmt.Println("Error parsing URL:", err)
		return
	}

	// 获取查询参数
	queryParams := parsedURL.Query()

	// 获取 'xtags' 参数的值
	xtags := queryParams.Get("xtags")

	// 打印 'xtags' 的值
	fmt.Println("xtags:", xtags)
}
