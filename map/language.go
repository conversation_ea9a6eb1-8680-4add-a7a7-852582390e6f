package language

import (
	_ "embed"
	"encoding/json"
	"strings"
)

//go:embed "language.json"
var _languageJson []byte

type Language struct {
	Code map[string]string `json:"languageCodes"`
	Name map[string]string `json:"languageNames"`
}

var languageData Language

func init() {
	err := json.Unmarshal(_languageJson, &languageData)
	if err != nil {
		languageData = Language{
			Code: *new(map[string]string),
			Name: *new(map[string]string),
		}
	}
}

func GetISOLanguageCode(language string) string {
	if language == "" {
		return "und"
	}

	// 转换为小写以进行匹配
	lowerLang := strings.ToLower(language)

	// 1. 尝试完整匹配
	if code, exists := languageData.Code[lowerLang]; exists {
		return code
	}

	// 2. 尝试匹配主要语言部分（如果有连字符）
	parts := strings.Split(lowerLang, "-")
	if len(parts) > 1 {
		if code, exists := languageData.Code[parts[0]]; exists {
			return code
		}
	}

	// 3. 尝试匹配每个部分
	for _, part := range parts {
		if code, exists := languageData.Code[part]; exists {
			return code
		}
	}

	// 4. 默认返回未定义
	return "und"
}

func GetLanguageName(language string) string {
	if language == "" {
		return ""
	}

	// 转换为小写以进行匹配
	langKey := strings.ToLower(language)

	// 获取简化的语言键（如果有连字符，取第一部分）
	simpleLangKey := strings.Split(langKey, "-")[0]

	// 尝试匹配完整键或简化键
	title := ""
	if name, exists := languageData.Name[langKey]; exists {
		title = name
	} else if name, exists := languageData.Name[simpleLangKey]; exists {
		title = name
	}

	// 如果还没找到，尝试首字母大写的格式
	if title == "" {
		parts := strings.Split(langKey, "-")
		var capitalizedParts []string
		for _, part := range parts {
			if len(part) > 0 {
				capitalizedPart := strings.ToUpper(string(part[0])) + part[1:]
				capitalizedParts = append(capitalizedParts, capitalizedPart)
			}
		}
		capitalizedKey := strings.Join(capitalizedParts, "-")
		if name, exists := languageData.Name[capitalizedKey]; exists {
			title = name
		}
	}

	return title
}
